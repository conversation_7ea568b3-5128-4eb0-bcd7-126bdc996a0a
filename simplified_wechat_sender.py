import logging
import pyperclip
import platform
import subprocess
import time
from typing import List, Dict, Any

class SimplifiedWeChatSender:
    """
    简化微信发送器，支持本地微信发送（剪贴板/系统通知）
    移除了企业微信机器人依赖
    """

    def __init__(self, contact_name: str = ""):
        self.contact_name = contact_name
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"SimplifiedWeChatSender initialized for contact: {contact_name}")

    def _send_via_clipboard_and_notify(self, message: str) -> bool:
        """
        通过剪贴板发送消息并提供系统通知
        """
        try:
            pyperclip.copy(message)
            self.logger.info("消息已复制到剪贴板。")

            # 尝试发送系统通知
            if platform.system() == "Windows":
                try:
                    # Windows 10+ 可以使用 PowerShell 发送 Toast 通知
                    # 这里简化为直接调用 PowerShell，如果用户没有安装相关库，会提示手动粘贴
                    script = f"""
                    [Windows.UI.Notifications.ToastNotificationManager, Windows.UI.Notifications, ContentType=WindowsRuntime] | Out-Null
                    $Template = [Windows.UI.Notifications.ToastNotificationManager]::GetTemplateContent([Windows.UI.Notifications.ToastTemplateType]::ToastText02)
                    $Template.GetElementsByTagName('text')[0].AppendChild($Template.CreateTextNode('旺店通取消单据通知')) | Out-Null
                    $Template.GetElementsByTagName('text')[1].AppendChild($Template.CreateTextNode('消息已复制到剪贴板，请粘贴到微信。')) | Out-Null
                    $Toast = [Windows.UI.Notifications.ToastNotification]::new($Template)
                    [Windows.UI.Notifications.ToastNotificationManager]::CreateToastNotifier('旺店通取消单据获取').Show($Toast)
                    """
                    subprocess.Popen(["powershell", "-Command", script], shell=True)
                    self.logger.info("已发送系统通知（Windows）。")
                except Exception as e:
                    self.logger.warning(f"无法发送Windows系统通知，请确保已安装相关库或手动粘贴: {e}")
            elif platform.system() == "Darwin": # macOS
                try:
                    # macOS 可以使用 osascript 发送通知
                    subprocess.run(['osascript', '-e', f'display notification "消息已复制到剪贴板，请粘贴到微信。" with title "旺店通取消单据通知"'])
                    self.logger.info("已发送系统通知（macOS）。")
                except Exception as e:
                    self.logger.warning(f"无法发送macOS系统通知，请确保已安装相关库或手动粘贴: {e}")
            else:
                self.logger.info("非Windows/macOS系统，请手动粘贴剪贴板内容。")

            return True
        except Exception as e:
            self.logger.error(f"通过剪贴板发送消息失败: {e}")
            return False

    def send_jd_cancel_orders(self, jd_numbers: List[str], contact: str) -> bool:
        """
        发送京东取消订单通知
        """
        if not jd_numbers:
            self.logger.info("没有京东物流单号，不发送取消订单通知。")
            return True

        message_parts = [
            "物流单号"
        ]
        for number in jd_numbers:
            message_parts.append(number)
        message_parts.append(f"\n以上京东单号订单取消，实物未发出共计{len(jd_numbers)}单，请处理！")

        message = "\n".join(message_parts)
        self.logger.info(f"准备发送京东取消订单通知给 {contact}: \n{message}")
        return self._send_via_clipboard_and_notify(message)

    def send_text_message(self, message: str, contact: str) -> bool:
        """
        发送普通文本消息
        """
        self.logger.info(f"准备发送文本消息给 {contact}: {message}")
        return self._send_via_clipboard_and_notify(message)

    def test_wechat_availability(self) -> Dict[str, bool]:
        """
        测试微信可用性（模拟）
        """
        self.logger.info("测试微信可用性...")
        # 简化测试，假设剪贴板和系统通知总是可用
        # 实际应用中可能需要更复杂的逻辑来检测微信客户端是否运行
        return {
            "local_wechat": True,  # 假设本地微信可用（通过剪贴板和通知）
            "clipboard_method": True,
            "system_notification": True,
            "error": None
        }