#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的代码
验证所有模块是否可以正常导入和初始化
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """测试所有模块的导入"""
    print("=" * 60)
    print("🔍 测试模块导入")
    print("=" * 60)
    
    modules_to_test = [
        ('config', 'WDTConfig'),
        ('wdt_post_client', 'WDTPostClient'),
        ('simplified_wechat_sender', 'SimplifiedWeChatSender'),
        ('stockout_details_manager', 'StockoutDetailsManager'),
        ('jd_cancel_manager_v2', 'JDCancelManagerV2'),
        ('jd_cancel_orders', 'JDCancelOrdersManager'),
        ('jd_cancel_gui', 'JDCancelGUI')
    ]
    
    success_count = 0
    total_count = len(modules_to_test)
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name)
            if hasattr(module, class_name):
                print(f"✅ {module_name}.{class_name} - 导入成功")
                success_count += 1
            else:
                print(f"❌ {module_name}.{class_name} - 类不存在")
        except Exception as e:
            print(f"❌ {module_name}.{class_name} - 导入失败: {e}")
            traceback.print_exc()
    
    print(f"\n📊 导入测试结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def test_config_validation():
    """测试配置验证"""
    print("\n" + "=" * 60)
    print("🔧 测试配置验证")
    print("=" * 60)
    
    try:
        from config import WDTConfig
        
        # 显示配置信息（隐藏敏感信息）
        config_info = WDTConfig.get_config_info()
        print("📋 当前配置:")
        for key, value in config_info.items():
            print(f"  {key}: {value}")
        
        # 验证配置
        WDTConfig.validate_config()
        print("✅ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def test_wechat_sender():
    """测试微信发送器"""
    print("\n" + "=" * 60)
    print("📱 测试微信发送器")
    print("=" * 60)
    
    try:
        from simplified_wechat_sender import SimplifiedWeChatSender
        
        # 创建发送器实例
        sender = SimplifiedWeChatSender(contact_name="测试联系人")
        print("✅ SimplifiedWeChatSender 初始化成功")
        
        # 测试可用性检查
        availability = sender.test_wechat_availability()
        print(f"📊 微信可用性: {availability}")
        
        return True
        
    except Exception as e:
        print(f"❌ 微信发送器测试失败: {e}")
        traceback.print_exc()
        return False

def test_managers():
    """测试管理器类"""
    print("\n" + "=" * 60)
    print("🏗️ 测试管理器类")
    print("=" * 60)
    
    try:
        # 测试 StockoutDetailsManager
        from stockout_details_manager import StockoutDetailsManager
        stockout_manager = StockoutDetailsManager()
        print("✅ StockoutDetailsManager 初始化成功")
        
        # 测试 JDCancelManagerV2
        from jd_cancel_manager_v2 import JDCancelManagerV2
        jd_manager = JDCancelManagerV2(wechat_contact="测试联系人")
        print("✅ JDCancelManagerV2 初始化成功")
        
        # 测试 JDCancelOrdersManager
        from jd_cancel_orders import JDCancelOrdersManager
        orders_manager = JDCancelOrdersManager()
        print("✅ JDCancelOrdersManager 初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 管理器测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的代码")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("配置验证", test_config_validation()))
    test_results.append(("微信发送器", test_wechat_sender()))
    test_results.append(("管理器类", test_managers()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(test_results)
    print(f"\n🎯 总体结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！代码修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")
        traceback.print_exc()
        sys.exit(1)
