#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试GUI启动
验证GUI界面是否可以正常创建和初始化
"""

import sys
import tkinter as tk
from datetime import datetime

def test_gui_creation():
    """测试GUI创建"""
    print("🖥️ 测试GUI创建...")
    
    try:
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，避免实际显示
        
        # 导入并创建GUI
        from jd_cancel_gui import JDCancelGUI
        app = JDCancelGUI(root)
        
        print("✅ GUI创建成功")
        print(f"   窗口标题: {root.title()}")
        print(f"   窗口大小: {root.geometry()}")
        
        # 测试一些基本属性
        if hasattr(app, 'wechat_contact'):
            print(f"   微信联系人: {app.wechat_contact.get()}")
        
        if hasattr(app, 'timer_mode'):
            print(f"   定时模式: {app.timer_mode.get()}")
        
        if hasattr(app, 'auto_start'):
            print(f"   自动启动: {app.auto_start.get()}")
        
        # 清理
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n📋 测试配置加载...")
    
    try:
        import json
        import os
        
        config_file = "jd_cancel_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("✅ 配置文件加载成功")
            print(f"   微信联系人: {config.get('wechat_contact', '未设置')}")
            print(f"   定时模式: {config.get('timer_mode', '未设置')}")
            print(f"   自动启动: {config.get('auto_start', False)}")
            print(f"   每日时间: {config.get('daily_times', [])}")
            
        else:
            print("⚠️ 配置文件不存在，将使用默认配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试GUI启动")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("配置加载", test_config_loading()))
    test_results.append(("GUI创建", test_gui_creation()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(test_results)
    print(f"\n🎯 总体结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("🎉 GUI启动测试通过！可以正常运行GUI应用！")
        print("\n💡 使用方法:")
        print("   python jd_cancel_gui.py")
        return True
    else:
        print("⚠️ GUI启动测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
