#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取已取消销售订单的京东物流单号模块
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient
from wechat_sender.sender import Sender
from config import WDTConfig

class JDCancelOrdersManager:
    """京东取消订单管理器"""
    
    def __init__(self):
        """
        初始化管理器
        """
        self.client = WDTPostClient()
        self.wechat_sender = Sender(WDTConfig.WECHAT_WEBHOOK_URL)
        self.wechat_contact = WDTConfig.WECHAT_CONTACT
        self.logger = logging.getLogger(__name__)
        
    def get_canceled_stockouts_today(self) -> List[Dict[str, Any]]:
        """
        获取当天已取消的出库单

        Returns:
            已取消出库单列表
        """
        # 获取当天时间范围
        now = datetime.now()
        start_time = datetime.combine(now.date(), datetime.min.time())
        end_time = now

        return self.get_canceled_stockouts(
            start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time.strftime('%Y-%m-%d %H:%M:%S')
        )

    def get_canceled_orders_today(self) -> List[Dict[str, Any]]:
        """
        获取当天已取消的销售订单（兼容性方法）

        Returns:
            已取消出库单列表
        """
        return self.get_canceled_stockouts_today()
    
    def get_canceled_stockouts(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内已取消的出库单（包含物流单号）

        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)

        Returns:
            已取消出库单列表
        """
        self.logger.info(f"开始查询{start_time}至{end_time}期间已取消的出库单...")

        page_size = 100
        page_no = 0
        all_stockouts = []

        # 构建查询参数
        base_params = {
            "start_consign_time": start_time,
            "end_consign_time": end_time,
            "page_size": page_size
        }

        while True:
            try:
                self.logger.debug(f"正在查询第{page_no + 1}页...")

                # 当前页参数
                current_params = {
                    **base_params,
                    "page_no": page_no
                }

                # 调用出库单查询API
                result = self.client.call_api('stockout.query', current_params)

                if not result:
                    break

                # 获取数据
                content = result.get('content', [])
                page_data = []
                if isinstance(content, list):
                    page_data = content
                elif isinstance(content, dict):
                    page_data = content.get('content', []) or content.get('stockouts', [])

                if not page_data:
                    break

                # 筛选已取消(status=5)的数据
                filtered_data = []
                for item in page_data:
                    if isinstance(item, dict):
                        status = str(item.get('status', ''))
                        if status == '5':  # 已取消状态
                            filtered_data.append(item)

                all_stockouts.extend(filtered_data)

                count = len(filtered_data)
                if count > 0:
                    self.logger.info(f"第{page_no + 1}页查询到{count}条已取消出库单")
                else:
                    self.logger.debug("当前页没有查询到已取消出库单")

                page_no += 1

                # 检查是否还有更多数据
                if len(page_data) < page_size:
                    break

                # 防止无限循环
                if page_no >= 50:
                    self.logger.warning("已查询50页，停止查询")
                    break

            except Exception as e:
                self.logger.error(f"查询失败: {str(e)}")
                break

        self.logger.info(f"共查询到{len(all_stockouts)}条已取消的出库单")
        return all_stockouts

    def get_canceled_orders(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内已取消的销售订单（兼容性方法）
        实际调用出库单查询来获取物流单号

        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)

        Returns:
            已取消出库单列表
        """
        return self.get_canceled_stockouts(start_time, end_time)
    
    def extract_jd_logistics_numbers(self, stockouts: List[Dict[str, Any]]) -> List[str]:
        """
        从出库单列表中提取京东物流单号

        Args:
            stockouts: 出库单列表

        Returns:
            京东物流单号列表
        """
        jd_numbers = []

        for stockout in stockouts:
            # 尝试从不同字段获取物流单号
            logistics_fields = ['logistics_no', 'express_no', 'tracking_no', 'waybill_no', 'express_number']

            for field in logistics_fields:
                logistics_no = stockout.get(field, '')
                if logistics_no and str(logistics_no).upper().startswith('JD'):
                    jd_numbers.append(str(logistics_no))
                    self.logger.debug(f"找到京东物流单号: {logistics_no} (出库单号: {stockout.get('stockout_no', '未知')})")
                    break

        # 去重并排序
        unique_jd_numbers = sorted(list(set(jd_numbers)))
        self.logger.info(f"共提取到{len(unique_jd_numbers)}个京东物流单号")

        return unique_jd_numbers
    
    def send_jd_cancel_notification(self, jd_numbers: List[str]) -> bool:
        """
        发送京东取消订单通知
        
        Args:
            jd_numbers: 京东物流单号列表
            
        Returns:
            发送是否成功
        """
        if not jd_numbers:
            self.logger.info("没有京东物流单号需要发送")
            return True
        
        # 构建@用户列表
        mentioned_list = []
        if self.wechat_contact:
            mentioned_list.append(self.wechat_contact)
        
        success = self.wechat_sender.send_jd_cancel_orders(jd_numbers, self.wechat_contact)
        
        if success:
            self.logger.info(f"成功发送{len(jd_numbers)}个京东取消订单通知")
        else:
            self.logger.error("发送京东取消订单通知失败")
        
        return success
    
    def process_today_jd_cancellations(self) -> Dict[str, Any]:
        """
        处理当天的京东取消订单

        Returns:
            处理结果统计
        """
        try:
            # 获取当天已取消出库单
            canceled_stockouts = self.get_canceled_stockouts_today()

            # 提取京东物流单号
            jd_numbers = self.extract_jd_logistics_numbers(canceled_stockouts)

            # 发送微信通知
            send_success = False
            if jd_numbers:
                send_success = self.send_jd_cancel_notification(jd_numbers)

            result = {
                'total_canceled_orders': len(canceled_stockouts),
                'jd_logistics_count': len(jd_numbers),
                'jd_logistics_numbers': jd_numbers,
                'notification_sent': send_success,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            self.logger.info(f"处理完成: 总取消出库单{len(canceled_stockouts)}个，京东物流单号{len(jd_numbers)}个")

            return result

        except Exception as e:
            self.logger.error(f"处理京东取消订单失败: {e}")
            return {
                'total_canceled_orders': 0,
                'jd_logistics_count': 0,
                'jd_logistics_numbers': [],
                'notification_sent': False,
                'error': str(e),
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def test_wechat_connection(self) -> bool:
        """
        测试微信连接
        
        Returns:
            连接是否正常
        """
        status, msg = self.wechat_sender.send('测试微信连接成功！')
        if not status:
            self.logger.error(f"微信连接测试失败: {msg}")
        return status

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """主函数 - 用于测试"""
    logger = setup_logging()
    
    # 示例配置
    try:
        # 创建管理器
        manager = JDCancelOrdersManager()
        
        # 测试微信连接
        logger.info("测试微信连接...")
        if manager.test_wechat_connection():
            logger.info("✅ 微信连接测试成功")
        else:
            logger.error("❌ 微信连接测试失败")
            return
        
        # 处理当天的京东取消订单
        logger.info("开始处理当天的京东取消订单...")
        result = manager.process_today_jd_cancellations()
        
        # 输出结果
        logger.info("处理结果:")
        logger.info(f"  总取消订单数: {result['total_canceled_orders']}")
        logger.info(f"  京东物流单号数: {result['jd_logistics_count']}")
        logger.info(f"  通知发送状态: {'成功' if result['notification_sent'] else '失败'}")
        
        if result['jd_logistics_numbers']:
            logger.info("  京东物流单号:")
            for number in result['jd_logistics_numbers']:
                logger.info(f"    {number}")
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")

if __name__ == '__main__':
    main()
